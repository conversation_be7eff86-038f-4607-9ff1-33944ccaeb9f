#!/bin/bash

# 小商店订单管理系统本地启动脚本 (非Docker方式)

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印函数
print_success() {
    echo -e "${GREEN}$1${NC}"
}

print_error() {
    echo -e "${RED}$1${NC}"
}

print_warning() {
    echo -e "${YELLOW}$1${NC}"
}

print_info() {
    echo -e "${BLUE}$1${NC}"
}

# 检查系统要求
check_requirements() {
    print_info "检查系统要求..."

    # 检查Go
    if ! command -v go &> /dev/null; then
        print_error "Go 未安装，请先安装 Go 1.19+"
        print_info "下载地址: https://golang.org/dl/"
        exit 1
    fi

    # 检查Node.js
    if ! command -v node &> /dev/null; then
        print_error "Node.js 未安装，请先安装 Node.js 16+"
        print_info "下载地址: https://nodejs.org/"
        exit 1
    fi

    # 检查npm
    if ! command -v npm &> /dev/null; then
        print_error "npm 未安装，请检查 Node.js 安装"
        exit 1
    fi

    print_success "系统要求检查通过"
}

# 创建必要目录
create_directories() {
    print_info "创建必要的目录..."
    mkdir -p backend/data
    mkdir -p backend/uploads
    print_success "目录创建完成"
}

# 设置环境变量文件
setup_env_files() {
    print_info "设置环境变量文件..."

    # 设置后端环境变量
    if [ ! -f "backend/.env" ]; then
        if [ -f "backend/.env.example" ]; then
            cp backend/.env.example backend/.env
            print_success "已创建 backend/.env 文件"
        else
            cat > backend/.env << EOF
PORT=8082
DB_TYPE=sqlite
DB_PATH=./data/shop_order.db
JWT_SECRET=your-secret-key-here
EOF
            print_success "已创建 backend/.env 文件"
        fi
    else
        print_info "backend/.env 文件已存在"
    fi

    # 设置前端环境变量
    if [ ! -f "shop-order-system/.env" ]; then
        cat > shop-order-system/.env << EOF
VITE_API_BASE_URL=http://localhost:8082/api
VITE_APP_TITLE=小商店订单管理系统
EOF
        print_success "已创建 shop-order-system/.env 文件"
    else
        print_info "shop-order-system/.env 文件已存在"
    fi
}

# 安装依赖
install_dependencies() {
    print_info "安装依赖..."

    # 安装前端依赖
    print_info "安装前端依赖..."
    cd shop-order-system
    npm install
    cd ..
    print_success "前端依赖安装完成"

    # 安装后端依赖
    print_info "安装后端依赖..."
    cd backend
    go mod tidy
    cd ..
    print_success "后端依赖安装完成"
}

# 启动后端服务
start_backend() {
    print_info "启动后端服务..."
    cd backend
    
    # 在后台启动后端服务
    nohup go run main.go > ../backend.log 2>&1 &
    BACKEND_PID=$!
    echo $BACKEND_PID > ../backend.pid
    cd ..

    # 等待后端服务启动
    local max_attempts=30
    local attempt=0

    while [ $attempt -lt $max_attempts ]; do
        if curl -s http://localhost:8082/health > /dev/null 2>&1; then
            print_success "后端服务已启动 (PID: $BACKEND_PID)"
            return 0
        fi
        
        attempt=$((attempt + 1))
        print_info "等待后端服务启动... ($attempt/$max_attempts)"
        sleep 2
    done

    print_error "后端服务启动超时"
    return 1
}

# 启动前端服务
start_frontend() {
    print_info "启动前端服务..."
    cd shop-order-system
    
    # 在后台启动前端服务
    nohup npm run dev > ../frontend.log 2>&1 &
    FRONTEND_PID=$!
    echo $FRONTEND_PID > ../frontend.pid
    cd ..

    # 等待前端服务启动
    local max_attempts=30
    local attempt=0

    while [ $attempt -lt $max_attempts ]; do
        if curl -s http://localhost:5173 > /dev/null 2>&1 || curl -s http://localhost:5174 > /dev/null 2>&1; then
            print_success "前端服务已启动 (PID: $FRONTEND_PID)"
            return 0
        fi
        
        attempt=$((attempt + 1))
        print_info "等待前端服务启动... ($attempt/$max_attempts)"
        sleep 2
    done

    print_error "前端服务启动超时"
    return 1
}

# 显示服务信息
show_service_info() {
    print_success "🎉 服务启动成功！"
    echo
    print_info "服务地址："
    echo "  前端应用: http://localhost:5173 或 http://localhost:5174"
    echo "  后端API:  http://localhost:8082"
    echo "  健康检查: http://localhost:8082/health"
    echo
    print_info "默认账户："
    echo "  管理员: <EMAIL> / demo123"
    echo "  员工:   <EMAIL> / demo123"
    echo
    print_info "日志文件："
    echo "  后端日志: backend.log"
    echo "  前端日志: frontend.log"
    echo
    print_info "停止服务："
    echo "  $0 stop"
}

# 停止服务
stop_services() {
    print_info "停止服务..."

    # 停止后端服务
    if [ -f "backend.pid" ]; then
        BACKEND_PID=$(cat backend.pid)
        if kill -0 $BACKEND_PID 2>/dev/null; then
            kill $BACKEND_PID
            print_success "后端服务已停止"
        fi
        rm -f backend.pid
    fi

    # 停止前端服务
    if [ -f "frontend.pid" ]; then
        FRONTEND_PID=$(cat frontend.pid)
        if kill -0 $FRONTEND_PID 2>/dev/null; then
            kill $FRONTEND_PID
            print_success "前端服务已停止"
        fi
        rm -f frontend.pid
    fi

    print_success "所有服务已停止"
}

# 显示帮助
show_help() {
    echo "小商店订单管理系统本地启动脚本 (非Docker)"
    echo
    echo "用法: $0 [命令]"
    echo
    echo "命令:"
    echo "  start     启动服务 (后端 + 前端)"
    echo "  backend   仅启动后端服务"
    echo "  frontend  仅启动前端服务"
    echo "  install   安装依赖"
    echo "  stop      停止服务"
    echo "  status    查看服务状态"
    echo "  logs      查看日志"
    echo "  help      显示帮助信息"
    echo
    echo "示例:"
    echo "  $0 start      # 启动完整服务"
    echo "  $0 backend    # 仅启动后端"
    echo "  $0 frontend   # 仅启动前端"
    echo "  $0 install    # 安装依赖"
    echo "  $0 stop       # 停止所有服务"
}

# 查看服务状态
show_status() {
    print_info "服务状态："
    
    # 检查后端服务
    if [ -f "backend.pid" ]; then
        BACKEND_PID=$(cat backend.pid)
        if kill -0 $BACKEND_PID 2>/dev/null; then
            print_success "后端服务: 运行中 (PID: $BACKEND_PID)"
        else
            print_error "后端服务: 已停止"
        fi
    else
        print_error "后端服务: 未启动"
    fi

    # 检查前端服务
    if [ -f "frontend.pid" ]; then
        FRONTEND_PID=$(cat frontend.pid)
        if kill -0 $FRONTEND_PID 2>/dev/null; then
            print_success "前端服务: 运行中 (PID: $FRONTEND_PID)"
        else
            print_error "前端服务: 已停止"
        fi
    else
        print_error "前端服务: 未启动"
    fi
}

# 查看日志
show_logs() {
    local service=$1
    
    if [ "$service" = "backend" ]; then
        if [ -f "backend.log" ]; then
            tail -f backend.log
        else
            print_error "后端日志文件不存在"
        fi
    elif [ "$service" = "frontend" ]; then
        if [ -f "frontend.log" ]; then
            tail -f frontend.log
        else
            print_error "前端日志文件不存在"
        fi
    else
        print_info "查看所有日志 (Ctrl+C 退出):"
        if [ -f "backend.log" ] && [ -f "frontend.log" ]; then
            tail -f backend.log frontend.log
        elif [ -f "backend.log" ]; then
            tail -f backend.log
        elif [ -f "frontend.log" ]; then
            tail -f frontend.log
        else
            print_error "没有找到日志文件"
        fi
    fi
}

# 主函数
main() {
    local command=${1:-start}

    case $command in
        start)
            check_requirements
            create_directories
            setup_env_files
            start_backend
            start_frontend
            show_service_info
            ;;
        backend)
            check_requirements
            create_directories
            setup_env_files
            start_backend
            print_success "后端服务已启动在 http://localhost:8082"
            ;;
        frontend)
            start_frontend
            print_success "前端服务已启动"
            ;;
        install)
            check_requirements
            install_dependencies
            ;;
        stop)
            stop_services
            ;;
        status)
            show_status
            ;;
        logs)
            show_logs $2
            ;;
        help)
            show_help
            ;;
        *)
            print_error "未知命令: $command"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
