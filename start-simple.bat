@echo off
echo Shop Order System - Local Startup Script

set "command=%~1"
if "%command%"=="" set "command=start"

if "%command%"=="help" goto :show_help
if "%command%"=="backend" goto :start_backend
if "%command%"=="frontend" goto :start_frontend
if "%command%"=="start" goto :start_all

echo Unknown command: %command%
goto :show_help

:start_all
echo [INFO] Starting both backend and frontend services...
call :start_backend
call :start_frontend
echo [SUCCESS] Services started successfully!
echo.
echo Service addresses:
echo   Frontend: http://localhost:5173 or http://localhost:5174
echo   Backend:  http://localhost:8082
echo   Health:   http://localhost:8082/health
echo.
echo Default accounts:
echo   Admin: <EMAIL> / demo123
echo   Staff: <EMAIL> / demo123
goto :end

:start_backend
echo [INFO] Starting backend service...
if not exist "backend\data" mkdir "backend\data"
if not exist "backend\uploads" mkdir "backend\uploads"

if not exist "backend\.env" (
    echo PORT=8082 > "backend\.env"
    echo DB_TYPE=sqlite >> "backend\.env"
    echo DB_PATH=./data/shop_order.db >> "backend\.env"
    echo JWT_SECRET=your-secret-key-here >> "backend\.env"
    echo [SUCCESS] Created backend\.env file
)

cd backend
start "Backend Service" cmd /k "echo Backend starting... && go run main.go"
cd ..
echo [SUCCESS] Backend service started
goto :eof

:start_frontend
echo [INFO] Starting frontend service...
if not exist "shop-order-system\.env" (
    echo VITE_API_BASE_URL=http://localhost:8082/api > "shop-order-system\.env"
    echo VITE_APP_TITLE=Shop Order System >> "shop-order-system\.env"
    echo [SUCCESS] Created shop-order-system\.env file
)

cd shop-order-system
start "Frontend Service" cmd /k "echo Frontend starting... && npm run dev"
cd ..
echo [SUCCESS] Frontend service started
goto :eof

:show_help
echo Shop Order System Local Startup Script
echo.
echo Usage: %~nx0 [command]
echo.
echo Commands:
echo   start     Start both backend and frontend services
echo   backend   Start only backend service
echo   frontend  Start only frontend service
echo   help      Show this help message
echo.
echo Examples:
echo   %~nx0 start      # Start complete system
echo   %~nx0 backend    # Start only backend
echo   %~nx0 frontend   # Start only frontend
goto :end

:end
