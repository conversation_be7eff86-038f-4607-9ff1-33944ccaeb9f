@echo off
echo ========================================
echo 小商店订单管理系统 - 开发环境启动脚本
echo ========================================
echo.

echo [1/3] 检查环境...
where node >nul 2>nul
if %errorlevel% neq 0 (
    echo 错误: 未找到 Node.js，请先安装 Node.js 16+
    pause
    exit /b 1
)

where go >nul 2>nul
if %errorlevel% neq 0 (
    echo 错误: 未找到 Go，请先安装 Go 1.19+
    pause
    exit /b 1
)

echo [2/3] 启动后端服务...
cd backend
start "后端服务" cmd /k "go run main.go"
cd ..

echo [3/3] 启动前端服务...
cd shop-order-system
start "前端服务" cmd /k "npm run dev"
cd ..

echo.
echo ========================================
echo 启动完成！
echo 前端地址: http://localhost:5173
echo 后端地址: http://localhost:8080
echo ========================================
echo.
echo 按任意键关闭此窗口...
pause >nul
