@echo off
setlocal enabledelayedexpansion

:: Shop Order System Local Startup Script (Non-Docker)

set "command=%~1"
set "option=%~2"

if "%command%"=="" set "command=start"

goto :main

:print_success
echo [SUCCESS] %~1
goto :eof

:print_error
echo [ERROR] %~1
goto :eof

:print_warning
echo [WARNING] %~1
goto :eof

:print_info
echo [INFO] %~1
goto :eof

:check_requirements
call :print_info "Checking system requirements..."

:: Check Go
where go >nul 2>&1
if errorlevel 1 (
    call :print_error "Go not installed, please install Go 1.19+"
    call :print_info "Download: https://golang.org/dl/"
    exit /b 1
)

:: Check Node.js
where node >nul 2>&1
if errorlevel 1 (
    call :print_error "Node.js not installed, please install Node.js 16+"
    call :print_info "Download: https://nodejs.org/"
    exit /b 1
)

:: Check npm
where npm >nul 2>&1
if errorlevel 1 (
    call :print_error "npm not installed, please check Node.js installation"
    exit /b 1
)

call :print_success "System requirements check passed"
goto :eof

:create_directories
call :print_info "Creating necessary directories..."
if not exist "backend\data" mkdir "backend\data"
if not exist "backend\uploads" mkdir "backend\uploads"
call :print_success "Directories created"
goto :eof

:setup_env_files
call :print_info "Setting up environment files..."

:: Setup backend environment
if not exist "backend\.env" (
    if exist "backend\.env.example" (
        copy "backend\.env.example" "backend\.env" >nul
        call :print_success "Created backend\.env file"
    ) else (
        echo PORT=8082 > "backend\.env"
        echo DB_TYPE=sqlite >> "backend\.env"
        echo DB_PATH=./data/shop_order.db >> "backend\.env"
        echo JWT_SECRET=your-secret-key-here >> "backend\.env"
        call :print_success "Created backend\.env file"
    )
) else (
    call :print_info "backend\.env file already exists"
)

:: Setup frontend environment
if not exist "shop-order-system\.env" (
    echo VITE_API_BASE_URL=http://localhost:8082/api > "shop-order-system\.env"
    echo VITE_APP_TITLE=Shop Order System >> "shop-order-system\.env"
    call :print_success "Created shop-order-system\.env file"
) else (
    call :print_info "shop-order-system\.env file already exists"
)
goto :eof

:install_dependencies
call :print_info "安装依赖..."

:: 安装前端依赖
call :print_info "安装前端依赖..."
cd shop-order-system
npm install
if errorlevel 1 (
    call :print_error "前端依赖安装失败"
    cd ..
    exit /b 1
)
cd ..
call :print_success "前端依赖安装完成"

:: 安装后端依赖
call :print_info "安装后端依赖..."
cd backend
go mod tidy
if errorlevel 1 (
    call :print_error "后端依赖安装失败"
    cd ..
    exit /b 1
)
cd ..
call :print_success "后端依赖安装完成"
goto :eof

:start_backend
call :print_info "启动后端服务..."
cd backend
start "后端服务" cmd /k "echo 后端服务启动中... && go run main.go"
cd ..

:: 等待后端服务启动
set /a max_attempts=30
set /a attempt=0

:wait_backend
if %attempt% geq %max_attempts% (
    call :print_error "后端服务启动超时"
    exit /b 1
)

curl -s http://localhost:8082/health >nul 2>&1
if errorlevel 1 (
    set /a attempt+=1
    call :print_info "等待后端服务启动... (!attempt!/%max_attempts%)"
    timeout /t 2 /nobreak >nul
    goto :wait_backend
)

call :print_success "后端服务已启动"
goto :eof

:start_frontend
call :print_info "启动前端服务..."
cd shop-order-system
start "前端服务" cmd /k "echo 前端服务启动中... && npm run dev"
cd ..

:: 等待前端服务启动
set /a max_attempts=30
set /a attempt=0

:wait_frontend
if %attempt% geq %max_attempts% (
    call :print_error "前端服务启动超时"
    exit /b 1
)

curl -s http://localhost:5173 >nul 2>&1
if not errorlevel 1 goto :frontend_ready

curl -s http://localhost:5174 >nul 2>&1
if not errorlevel 1 goto :frontend_ready

set /a attempt+=1
call :print_info "等待前端服务启动... (!attempt!/%max_attempts%)"
timeout /t 2 /nobreak >nul
goto :wait_frontend

:frontend_ready
call :print_success "前端服务已启动"
goto :eof

:show_service_info
call :print_success "🎉 服务启动成功！"
echo.
call :print_info "服务地址："
echo   前端应用: http://localhost:5173 或 http://localhost:5174
echo   后端API:  http://localhost:8082
echo   健康检查: http://localhost:8082/health
echo.
call :print_info "默认账户："
echo   管理员: <EMAIL> / demo123
echo   员工:   <EMAIL> / demo123
echo.
call :print_info "停止服务："
echo   关闭对应的命令行窗口即可停止服务
echo   或使用 Ctrl+C 停止单个服务
goto :eof

:stop_services
call :print_info "停止服务..."
call :print_warning "请手动关闭后端和前端服务的命令行窗口"
call :print_info "或在对应窗口中按 Ctrl+C 停止服务"
goto :eof

:show_help
echo 小商店订单管理系统本地启动脚本 (非Docker)
echo.
echo 用法: %~nx0 [命令]
echo.
echo 命令:
echo   start     启动服务 (后端 + 前端)
echo   backend   仅启动后端服务
echo   frontend  仅启动前端服务
echo   install   安装依赖
echo   stop      停止服务提示
echo   help      显示帮助信息
echo.
echo 示例:
echo   %~nx0 start      # 启动完整服务
echo   %~nx0 backend    # 仅启动后端
echo   %~nx0 frontend   # 仅启动前端
echo   %~nx0 install    # 安装依赖
goto :eof

:main
if "%command%"=="start" (
    call :check_requirements
    call :create_directories
    call :setup_env_files
    call :start_backend
    call :start_frontend
    call :show_service_info
) else if "%command%"=="backend" (
    call :check_requirements
    call :create_directories
    call :setup_env_files
    call :start_backend
    call :print_success "后端服务已启动在 http://localhost:8082"
) else if "%command%"=="frontend" (
    call :start_frontend
    call :print_success "前端服务已启动"
) else if "%command%"=="install" (
    call :check_requirements
    call :install_dependencies
) else if "%command%"=="stop" (
    call :stop_services
) else if "%command%"=="help" (
    call :show_help
) else (
    call :print_error "未知命令: %command%"
    call :show_help
    exit /b 1
)

goto :eof
